// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  // output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Users table cho Better Auth

model User {
  id            String   @id
  email         String   @unique @db.VarChar(255)
  name          String?
  image         String?
  emailVerified Boolean?
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @default(now()) @map("updated_at")

  // Relations
  posts    Post[]
  sessions Session[]
  accounts Account[]

  @@map("users")
}

// Sessions table cho Better Auth

model Session {
  id        String   @id
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  token     String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id])

  ipAddress String?
  userAgent String?

  @@map("sessions")
}

// Accounts table cho Better Auth (OAuth)

model Account {
  id           String    @id
  userId       String    @map("user_id")
  accountId    String    @map("account_id")
  providerId   String    @map("provider_id")
  accessToken  String?   @map("access_token")
  refreshToken String?   @map("refresh_token")
  idToken      String?   @map("id_token")
  expiresAt    DateTime? @map("expires_at")
  password     String? // for email/password auth
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id])

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?

  @@map("accounts")
}

// Verification table cho Better Auth

model Verification {
  id         String   @id
  identifier String
  value      String
  expiresAt  DateTime @map("expires_at")
  createdAt  DateTime @default(now()) @map("created_at")

  updatedAt DateTime?

  @@map("verifications")
}

// Posts table

model Post {
  id        String   @id
  title     String   @db.VarChar(255)
  content   String?
  published Boolean  @default(false)
  authorId  String   @map("author_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @map("updated_at")

  // Relations
  author User @relation(fields: [authorId], references: [id])

  @@map("posts")
}

model Jwks {
  id         String   @id
  publicKey  String
  privateKey String
  createdAt  DateTime

  @@map("jwks")
}
