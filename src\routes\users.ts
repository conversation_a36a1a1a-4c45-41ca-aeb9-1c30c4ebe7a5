import { Hono } from 'hono'
import { db } from '../db'
import { authMiddleware, requireAuth } from '../middleware/auth'
import { zValidator } from '@hono/zod-validator'
import { updateUserSchema, paginationSchema } from '../lib/validation'

// Define types for Hono context
type Variables = {
  user: any
  session: any
}

const usersRoutes = new Hono<{ Variables: Variables }>()

// Apply auth middleware to all routes
usersRoutes.use('*', authMiddleware)

// Get current user profile
usersRoutes.get('/profile', requireAuth, async (c) => {
  const user = c.get('user')
  return c.json({ user })
})

// Get all users (with pagination)
usersRoutes.get('/', zValidator('query', paginationSchema), async (c) => {
  const { page, limit } = c.req.valid('query')
  const offset = (page - 1) * limit

  try {
    const [allUsers, totalCount] = await Promise.all([
      db.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          createdAt: true,
        },
        skip: offset,
        take: limit
      }),
      db.user.count()
    ])

    return c.json({
      users: allUsers,
      pagination: {
        page,
        limit,
        total: totalCount,
      }
    })
  } catch (error) {
    console.error('Get users error:', error)
    return c.json({ error: 'Failed to fetch users' }, 500)
  }
})

// Get user by ID
usersRoutes.get('/:id', async (c) => {
  const id = c.req.param('id')

  try {
    const user = await db.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        createdAt: true,
      }
    })

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    return c.json({ user })
  } catch (error) {
    console.error('Get user error:', error)
    return c.json({ error: 'Failed to fetch user' }, 500)
  }
})

// Update current user profile
usersRoutes.put('/profile',
  requireAuth,
  zValidator('json', updateUserSchema),
  async (c) => {
    const currentUser = c.get('user')
    const data = c.req.valid('json')

    try {
      const updatedUser = await db.user.update({
        where: { id: currentUser.id },
        data: {
          ...data,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          updatedAt: true,
        }
      })

      return c.json({
        message: 'Profile updated successfully',
        user: updatedUser
      })
    } catch (error) {
      console.error('Update profile error:', error)
      return c.json({ error: 'Failed to update profile' }, 500)
    }
  }
)

export default usersRoutes
