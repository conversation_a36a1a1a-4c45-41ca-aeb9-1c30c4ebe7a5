import { BaseAgent } from '../base/agent'
import { AgentConfig } from '../../types'

export class ChatAgent extends BaseAgent {
  constructor(config?: Partial<AgentConfig>) {
    const defaultConfig: AgentConfig = {
      id: 'chat-agent',
      name: 'Chat Agent',
      description: 'Một AI agent thông minh cho việc trò chuyện tự nhiên',
      model: 'gpt-3.5-turbo',
      systemPrompt: `Bạn là một AI assistant thông minh và hữu ích. 
      H<PERSON>y trò chuyện một cách tự nhiên và cung cấp thông tin chính xác.
      Luôn lịch sự và tôn trọng người dùng.
      Trả lời bằng tiếng Việt trừ khi được yêu cầu sử dụng ngôn ngữ khác.`,
      temperature: 0.7,
      maxTokens: 2000,
      tools: [],
      enabled: true,
      ...config
    }
    super(defaultConfig)
  }

  protected initializeTools(): void {
    // Chat agent không cần tools đặc biệt
    // C<PERSON> thể thêm tools như weather, calculator, etc.
  }

  // Override để customize chat behavior
  protected buildSystemPrompt(): string {
    const basePrompt = super.buildSystemPrompt()
    const currentTime = new Date().toLocaleString('vi-VN', { 
      timeZone: 'Asia/Ho_Chi_Minh' 
    })
    
    return `${basePrompt}

Thời gian hiện tại: ${currentTime}

Quy tắc trả lời:
- Luôn thân thiện và hữu ích
- Trả lời ngắn gọn nhưng đầy đủ thông tin
- Nếu không biết câu trả lời, hãy thật thà thừa nhận
- Có thể sử dụng emoji để làm cho cuộc trò chuyện sinh động hơn
- Nếu được hỏi về bản thân, hãy giới thiệu mình là Chat Agent`
  }
}
