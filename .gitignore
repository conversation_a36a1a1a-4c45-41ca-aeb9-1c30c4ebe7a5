# deps
node_modules/

# build output
dist/
build/

# environment files
.env
.env.local
.env.production
.env.staging

# logs
*.log
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Database
drizzle/
*.sqlite
*.sqlite3
*.db

# Temporary files
.tmp/
.temp/

/src/generated/prisma
