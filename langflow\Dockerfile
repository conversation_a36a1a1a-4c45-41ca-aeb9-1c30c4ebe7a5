# Dockerfile for Langflow Custom Application
# Based on official Langflow Docker deployment pattern

FROM langflowai/langflow:latest

# Create flows directory
RUN mkdir -p /app/flows

# Copy flows to container
COPY ./flows/ /app/flows/

# Set environment variable for flow loading
ENV LANGFLOW_LOAD_FLOWS_PATH=/app/flows

# Expose port
EXPOSE 7860

# The base image already has the entrypoint configured
# No need to override CMD