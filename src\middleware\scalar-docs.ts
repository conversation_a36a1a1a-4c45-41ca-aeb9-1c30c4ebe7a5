import { Hono } from "hono";
import { <PERSON>ala<PERSON> } from "@scalar/hono-api-reference";
import { inspectRoutes } from "hono/dev";

// ===== TYPES & INTERFACES =====
type ScalarTheme = "alternate" | "default" | "moon" | "purple" | "solarized" | "bluePlanet" | "saturn" | "kepler" | "mars" | "deepSpace";

interface ScalarConfig {
  title?: string;
  theme?: ScalarTheme;
  path?: string;
  hiddenPaths?: string[];
  customCss?: string;
  serverUrl?: string;
}

interface RouteInfo {
  path: string;
  method: string;
}

// ===== CONSTANTS =====
const DEFAULT_CONFIG: Required<ScalarConfig> = {
  title: "API Documentation",
  theme: "alternate",
  path: "/docs",
  hiddenPaths: [],
  serverUrl: "/",
  customCss: `
.scalar-api-reference [data-method="get"] { --scalar-button-1: #10b981; --scalar-button-1-hover: #059669; }
.scalar-api-reference [data-method="post"] { --scalar-button-1: #3b82f6; --scalar-button-1-hover: #2563eb; }  
.scalar-api-reference [data-method="put"] { --scalar-button-1: #f59e0b; --scalar-button-1-hover: #d97706; }
.scalar-api-reference [data-method="delete"] { --scalar-button-1: #ef4444; --scalar-button-1-hover: #dc2626; }
.scalar-api-reference [data-method="patch"] { --scalar-button-1: #8b5cf6; --scalar-button-1-hover: #7c3aed; }
.scalar-api-reference .tag { font-weight: 600; margin-bottom: 12px; padding: 8px 12px; background: var(--scalar-background-2); border-radius: 6px; border-left: 4px solid var(--scalar-color-1); }`
};

const OPENAPI_PATH = "/openapi.json";

const RESPONSE_SCHEMAS = {
  success: {
    type: "object",
    properties: {
      success: { type: "boolean", example: true },
      data: { type: "object" }
    }
  },
  error: {
    type: "object", 
    properties: {
      success: { type: "boolean", example: false },
      error: { type: "string" }
    }
  }
} as const;

// ===== UTILITY FUNCTIONS =====
const extractTag = (path: string): string | null => {
  const segments = path.split("/").filter(Boolean);
  if (!segments.length || path === "/*") return null;
  
  // Bỏ qua "api" prefix vì basePath("/api")
  const relevantSegments = segments.filter(s => s !== "api");
  if (!relevantSegments.length) return null;
  
  const tag = relevantSegments[0]?.replace(/[{}:]/g, "");
  return tag ? tag.charAt(0).toUpperCase() + tag.slice(1) : null;
};

const isPathHidden = (path: string, hiddenPaths: string[]): boolean => {
  return hiddenPaths.some(hiddenPath => {
    if (hiddenPath.includes("*")) {
      const regex = new RegExp(hiddenPath.replace(/\*/g, ".*"));
      return regex.test(path);
    }
    return path === hiddenPath || path.startsWith(hiddenPath + "/");
  });
};

const isSystemPath = (path: string): boolean => {
  return path === "/docs" || path === OPENAPI_PATH;
};

const extractPathParams = (path: string) => {
  const params = path.match(/:(\w+)/g);
  return params?.map(param => ({
    name: param.substring(1),
    in: "path" as const,
    required: true,
    schema: { type: "string" },
    description: `The ${param.substring(1)} parameter`
  })) || [];
};

const needsRequestBody = (method: string): boolean => {
  return ["post", "put", "patch"].includes(method.toLowerCase());
};

const buildRequestBody = () => ({
  required: true,
  content: {
    "application/json": {
      schema: {
        type: "object",
        properties: {
          data: { type: "object", description: "Request payload" }
        },
        required: ["data"]
      }
    }
  }
});

const createOperation = (route: RouteInfo) => {
  const method = route.method.toLowerCase();
  const tag = extractTag(route.path);
  const cleanPath = route.path.replace("/api", "") || "/";
  
  const operation: any = {
    summary: `${route.method} ${cleanPath}`,
    description: `${route.method} operation for ${cleanPath}`,
    operationId: `${method}_${route.path.replace(/[{}\/]/g, "_").replace(/^_/, "")}`,
    responses: {
      "200": {
        description: "Successful response",
        content: { "application/json": { schema: RESPONSE_SCHEMAS.success }}
      },
      "400": {
        description: "Bad request", 
        content: { "application/json": { schema: RESPONSE_SCHEMAS.error }}
      }
    }
  };

  if (tag) operation.tags = [tag];
  
  const parameters = extractPathParams(route.path);
  if (parameters.length) operation.parameters = parameters;
  
  if (needsRequestBody(method)) {
    operation.requestBody = buildRequestBody();
  }

  return operation;
};

const buildPaths = (routes: RouteInfo[]) => {
  const paths: Record<string, any> = {};
  
  routes.forEach(route => {
    const pathKey = route.path.replace(/:(\w+)/g, "{$1}");
    const method = route.method.toLowerCase();
    
    if (!paths[pathKey]) paths[pathKey] = {};
    paths[pathKey][method] = createOperation(route);
  });
  
  return paths;
};

const extractUniqueTags = (routes: RouteInfo[]) => {
  const tags = new Set<string>();
  routes.forEach(route => {
    const tag = extractTag(route.path);
    if (tag) tags.add(tag);
  });
  
  return Array.from(tags).map(tag => ({
    name: tag,
    description: `${tag} related operations`
  }));
};

const buildOpenAPISpec = (routes: RouteInfo[], config: Required<ScalarConfig>) => {
  const paths = buildPaths(routes);
  const tags = extractUniqueTags(routes);
  
  return {
    openapi: "3.0.0",
    info: {
      title: config.title,
      version: "1.0.0",
      description: "Auto-generated API documentation"
    },
    servers: [{
      url: config.serverUrl,
      description: "Development server"
    }],
    ...(tags.length && { tags }),
    paths
  };
};

// ===== MAIN FUNCTION =====
function setupScalarDocs(app: Hono<any>, options: ScalarConfig = {}) {
  const config = { ...DEFAULT_CONFIG, ...options };

  // OpenAPI spec endpoint
  app.get(OPENAPI_PATH, (c) => {
    const allRoutes = inspectRoutes(app);
    const filteredRoutes = allRoutes.filter(route => 
      !isSystemPath(route.path) && !isPathHidden(route.path, config.hiddenPaths)
    );

    return c.json(buildOpenAPISpec(filteredRoutes, config));
  });

  // Docs endpoint
  app.get(config.path, Scalar({
    url: `/api${OPENAPI_PATH}`,
    theme: config.theme,
    layout: "modern",
    showSidebar: true,
    hideDownloadButton: false,
    hideTestRequestButton: false,
    metaData: {
      title: config.title,
      description: "Auto-generated API documentation"
    },
    customCss: config.customCss
  }));

  return app;
}

export { setupScalarDocs };
export type { ScalarConfig };