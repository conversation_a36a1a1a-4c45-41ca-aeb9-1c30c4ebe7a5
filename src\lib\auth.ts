import { betterAuth } from 'better-auth'
import { prismaAdapter } from 'better-auth/adapters/prisma'
import { jwt } from 'better-auth/plugins'
import { db } from '../db'
import { resend, resendFrom } from './email/resend'
import { reactResetPasswordEmail } from './email/reset-password'

export const auth = betterAuth({
    database: prismaAdapter(db, {
        provider: 'postgresql',
    }),
    emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
        async sendResetPassword({ user, url }) {
            await resend.emails.send({
                from: resendFrom,
                to: user.email,
                subject: "Reset your password",
                react: reactResetPasswordEmail({
                    username: user.email,
                    resetLink: url,
                }),
            })
        },
    },
    // user: {
    //     fields: {
    //         emailVerified: {
    //             type: 'boolean',
    //             defaultValue: false,
    //             required: false,
    //         },
    //     },
    // },
    emailVerification: {
        async sendVerificationEmail({ user, url }) {
            const res = await resend.emails.send({
                from: resendFrom,
                to: user.email,
                subject: "Verify your email address",
                html: `<a href="${url}">Verify your email address</a>`,
            })
            console.log(res, user.email)
        },
    },
    session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 24 hours
        cookieCache: {
            enabled: false, // Disable cookie caching
        },
        storeSessionInDatabase: true, // Store session in database instead of cookies
    },
    // user: {
    //     // additionalFields: {
    //     //     role: {
    //     //         type: 'string',
    //     //         defaultValue: 'user',
    //     //     },
    //     // },
    // },
    socialProviders: {
        github: {
            clientId: process.env.GITHUB_CLIENT_ID || '',
            clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
        },
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID || '',
            clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
        }
    },
    // Temporarily disable JWT plugin to fix infinite loop
    // plugins: [
    //     jwt({
    //         jwt: {
    //             issuer: process.env.BETTER_AUTH_URL || 'http://localhost:3000',
    //             audience: process.env.BETTER_AUTH_URL || 'http://localhost:3000',
    //             expirationTime: '1h',
    //         }
    //     })
    // ],
    secret: process.env.BETTER_AUTH_SECRET!,
    baseURL: process.env.BETTER_AUTH_URL!,
    advanced: {
        crossSubDomainCookies: {
            enabled: false,
        },
    },
})
