import { Context, Next } from 'hono'
import { auth } from '../lib/auth'
import { jwtVerify, createRemoteJWKSet } from 'jose'
import { db } from '../db'
import { sessionLimitMiddleware } from './session-limit'


// Middleware để check authentication - hỗ trợ cả JWT và session
export async function authMiddleware(c: Context, next: Next) {
    try {
        // Thử lấy JWT token từ Authorization header
        const authHeader = c.req.header('Authorization')
        if (authHeader?.startsWith('Bearer ')) {
            const token = authHeader.substring(7) // Remove 'Bearer ' prefix

            console.log('🔍 JWT Debug - Token received:', token.substring(0, 50) + '...')
            console.log('🔍 JWT Debug - Token length:', token.length)

            // Check if token is a valid JWT format (should have 3 parts separated by '.')
            const tokenParts = token.split('.')
            if (tokenParts.length === 3) {
                try {
                    // Verify JWT token
                    const jwksUrl = `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/jwks`
                    console.log('🔍 JWT Debug - JWKS URL:', jwksUrl)

                    const JWKS = createRemoteJWKSet(
                        new URL(jwksUrl)
                    )

                    console.log('🔍 JWT Debug - Attempting JWT verification with issuer:', process.env.BETTER_AUTH_URL || 'http://localhost:3000')

                    const { payload } = await jwtVerify(token, JWKS, {
                        issuer: process.env.BETTER_AUTH_URL || 'http://localhost:3000',
                        audience: process.env.BETTER_AUTH_URL || 'http://localhost:3000'
                    })

                    console.log('✅ JWT Debug - JWT verification successful, payload:', payload)

                    // Lưu thông tin user từ JWT payload
                    c.set('user', payload)
                    c.set('jwt', token)

                    await next()
                    return
                } catch (jwtError) {
                    console.error('❌ JWT Debug - JWT verification failed:', jwtError instanceof Error ? jwtError.message : String(jwtError))
                    console.error('❌ JWT Debug - Full error:', jwtError)
                    console.warn('JWT verification failed, falling back to session:', jwtError)
                    // JWT verification failed, fall back to session
                }
            } else {
                console.log('🔍 JWT Debug - Token is not JWT format, falling back to session')
                // Not a JWT token, fall back to session
            }
        }

        // Fallback to session-based authentication
        console.log('🔍 Session Debug - Attempting session authentication')
        console.log('🔍 Session Debug - Headers:', Object.keys(c.req.header()))

        // Try to get session from database using the token from Authorization header
        try {
            const authHeader = c.req.header('Authorization')
            const sessionToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null

            if (!sessionToken) {
                console.log('❌ Session Debug - No session token in Authorization header')
                return
            }

            // Query session from database
            const sessionData = await db.session.findFirst({
                where: {
                    token: sessionToken,
                    expiresAt: {
                        gt: new Date() // Check if session is not expired
                    }
                },
                include: {
                    user: true // Include user data
                }
            })

            console.log('🔍 Session Debug - Database session result:', sessionData)

            if (sessionData?.user) {
                console.log('✅ Session Debug - Session authentication successful')
                c.set('user', sessionData.user)
                c.set('session', {
                    id: sessionData.id,
                    userId: sessionData.userId,
                    token: sessionData.token,
                    expiresAt: sessionData.expiresAt,
                    createdAt: sessionData.createdAt,
                    updatedAt: sessionData.updatedAt
                })

                // 🔒 Áp dụng session limit middleware sau khi authenticate thành công
                console.log('🔒 Applying session limit middleware...')
                await sessionLimitMiddleware(c, async () => {
                    console.log('🔒 Session limit middleware completed')
                })
            } else {
                console.log('❌ Session Debug - Session not found or expired')
            }
        } catch (dbError) {
            console.error('❌ Session Debug - Database error:', dbError)
        }

        await next()
    } catch (error) {
        console.error('Auth middleware error:', error)
        await next()
    }
}

// Middleware yêu cầu phải đăng nhập
export async function requireAuth(c: Context, next: Next) {
    const user = c.get('user')

    if (!user) {
        return c.json({ error: 'Unauthorized' }, 401)
    }

    await next()
}

// Middleware để lấy JWT token
export async function getJWTToken(c: Context, next: Next) {
    try {
        // Lấy session token từ cookies hoặc headers
        const sessionToken = c.req.header('Authorization')?.replace('Bearer ', '') ||
            c.req.header('X-Session-Token')

        if (!sessionToken) {
            return c.json({ error: 'Session token required' }, 400)
        }

        // Lấy JWT token từ better-auth
        const response = await fetch(`${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/token`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${sessionToken}`,
                'Content-Type': 'application/json'
            }
        })

        if (!response.ok) {
            return c.json({ error: 'Failed to get JWT token' }, 500)
        }

        const data = await response.json()
        return c.json(data)
    } catch (error) {
        console.error('Get JWT token error:', error)
        return c.json({ error: 'Internal server error' }, 500)
    }
}
