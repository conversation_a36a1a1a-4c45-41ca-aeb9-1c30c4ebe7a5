/**
 * Agent Registry - <PERSON><PERSON><PERSON><PERSON> lý và đăng ký các AI agents
 *
 * Singleton registry chịu trách nhiệm:
 * - Quản lý vòng đời của các agent instances
 * - Đă<PERSON> ký và hủy đăng ký agents
 * - Tạo agents mớ<PERSON> từ config
 * - <PERSON><PERSON> cấp truy cập đến các agents đã đăng ký
 *
 * <PERSON><PERSON><PERSON> sử dụng:
 * ```typescript
 * const registry = AgentRegistry.getInstance()
 * const chatAgent = registry.getAgent('chat-agent')
 * const newAgent = registry.createAgent(agentConfig)
 * ```
 */

import { BaseAgent } from '../../agents/base/agent'
import { ChatAgent } from '../../agents/chat/chat-agent'
import { CodeAgent } from '../../agents/code/code-agent'
import { ResearchAgent } from '../../agents/research/research-agent'
import { AgentConfig, AgentType } from '../../types'

export class AgentRegistry {
  private agents: Map<string, BaseAgent> = new Map()
  private static instance: AgentRegistry

  private constructor() {
    this.initializeDefaultAgents()
  }

  public static getInstance(): AgentRegistry {
    if (!AgentRegistry.instance) {
      AgentRegistry.instance = new AgentRegistry()
    }
    return AgentRegistry.instance
  }

  private initializeDefaultAgents() {
    // Initialize default agents
    const chatAgent = new ChatAgent()
    const codeAgent = new CodeAgent()
    const researchAgent = new ResearchAgent()

    this.agents.set(chatAgent.getConfig().id, chatAgent)
    this.agents.set(codeAgent.getConfig().id, codeAgent)
    this.agents.set(researchAgent.getConfig().id, researchAgent)
  }

  registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.getConfig().id, agent)
  }

  unregisterAgent(agentId: string): boolean {
    return this.agents.delete(agentId)
  }

  getAgent(agentId: string): BaseAgent | undefined {
    return this.agents.get(agentId)
  }

  getAllAgents(): BaseAgent[] {
    return Array.from(this.agents.values())
  }

  getEnabledAgents(): BaseAgent[] {
    return this.getAllAgents().filter(agent => agent.getConfig().enabled)
  }

  getAgentsByType(type: AgentType): BaseAgent[] {
    return this.getAllAgents().filter(agent => {
      const config = agent.getConfig()
      // Simple type inference based on agent ID or description
      if (type === 'chat') return config.id.includes('chat')
      if (type === 'code') return config.id.includes('code')
      if (type === 'research') return config.id.includes('research')
      return config.id.includes('custom')
    })
  }

  createAgent(config: AgentConfig): BaseAgent {
    let agent: BaseAgent

    // Determine agent type and create appropriate instance
    if (config.id.includes('chat')) {
      agent = new ChatAgent(config)
    } else if (config.id.includes('code')) {
      agent = new CodeAgent(config)
    } else if (config.id.includes('research')) {
      agent = new ResearchAgent(config)
    } else {
      // Default to ChatAgent for custom agents
      agent = new ChatAgent(config)
    }

    this.registerAgent(agent)
    return agent
  }

  updateAgent(agentId: string, updates: Partial<AgentConfig>): boolean {
    const agent = this.getAgent(agentId)
    if (agent) {
      agent.updateConfig(updates)
      return true
    }
    return false
  }

  listAgentConfigs(): AgentConfig[] {
    return this.getAllAgents().map(agent => agent.getConfig())
  }

  // Health check method
  checkAgentHealth(agentId: string): boolean {
    const agent = this.getAgent(agentId)
    return agent !== undefined && agent.getConfig().enabled
  }
}
