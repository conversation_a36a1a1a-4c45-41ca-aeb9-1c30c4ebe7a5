import { BaseAgent } from '../base/agent'
import { AgentConfig, Tool } from '../../types'

export class ResearchAgent extends BaseAgent {
  constructor(config?: Partial<AgentConfig>) {
    const defaultConfig: AgentConfig = {
      id: 'research-agent',
      name: 'Research Agent',
      description: 'Một AI agent chuyên về nghiên cứu và tìm kiếm thông tin',
      model: 'gpt-4',
      systemPrompt: `Bạn là một AI research assistant chuyê<PERSON> nghiệ<PERSON>.
      Bạn giúp người dùng nghiên cứu, phân tích thông tin và tổng hợp kiến thức.
      
      Khả năng của bạn:
      - Tìm kiếm và phân tích thông tin từ nhiều nguồn
      - Tổng hợp và trích xuất insights từ dữ liệu
      - T<PERSON>o reports và summaries chi tiết
      - Fact-checking và verification
      - Comparative analysis
      - Trend analysis
      
      Methodology:
      - <PERSON><PERSON> dụng multiple sources để cross-reference
      - Cite sources khi có thể
      - Distinguish giữa facts và opinions
      - Acknowledge limitations và uncertainties
      - <PERSON><PERSON> cấp balanced perspectives`,
      temperature: 0.4,
      maxTokens: 3000,
      tools: ['web-search', 'fact-checker', 'summarizer'],
      enabled: true,
      ...config
    }
    super(defaultConfig)
  }

  protected initializeTools(): void {
    // Web search tool
    const webSearchTool: Tool = {
      name: 'web-search',
      description: 'Search the web for current information',
      parameters: {
        type: 'object',
        properties: {
          query: { type: 'string' },
          limit: { type: 'number', default: 10 }
        },
        required: ['query']
      },
      execute: async (params) => {
        // Trong production, integrate với search API như Serper, Bing, etc.
        return {
          results: [
            {
              title: `Search results for: ${params.query}`,
              url: 'https://example.com',
              snippet: 'Mock search result content...'
            }
          ],
          totalResults: 1
        }
      }
    }

    // Fact checker tool
    const factCheckerTool: Tool = {
      name: 'fact-checker',
      description: 'Verify facts and claims',
      parameters: {
        type: 'object',
        properties: {
          claim: { type: 'string' }
        },
        required: ['claim']
      },
      execute: async (params) => {
        // Implement fact-checking logic
        return {
          accuracy: 'high',
          sources: [],
          confidence: 0.8,
          notes: `Fact-checking result for: ${params.claim}`
        }
      }
    }

    // Summarizer tool
    const summarizerTool: Tool = {
      name: 'summarizer',
      description: 'Summarize long texts or multiple sources',
      parameters: {
        type: 'object',
        properties: {
          text: { type: 'string' },
          maxLength: { type: 'number', default: 300 }
        },
        required: ['text']
      },
      execute: async (params) => {
        // Implement summarization logic
        return {
          summary: `Summary of provided text (${params.maxLength} words max)`,
          keyPoints: [],
          sources: []
        }
      }
    }

    this.addTool(webSearchTool)
    this.addTool(factCheckerTool)
    this.addTool(summarizerTool)
  }

  protected buildSystemPrompt(): string {
    const basePrompt = super.buildSystemPrompt()
    
    return `${basePrompt}

Research Guidelines:

1. **Source Credibility**: Ưu tiên thông tin từ authoritative sources
2. **Timeliness**: Chú ý đến thời điểm của thông tin
3. **Bias**: Nhận biết và acknowledge potential biases
4. **Completeness**: Cung cấp comprehensive coverage của topic
5. **Citations**: Include references khi có thể
6. **Clarity**: Present findings một cách clear và organized

Output Format:
- Executive Summary
- Key Findings
- Detailed Analysis
- Sources and References
- Limitations and Caveats

Luôn maintain objectivity và present balanced viewpoints.`
  }
}
