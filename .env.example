# Example env for local development with docker-compose
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=postgres
# DATABASE_URL format used by the app and drizzle-kit
DATABASE_URL=postgres://postgres:postgres@localhost:5432/postgres

# Better Auth Configuration
BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production
BETTER_AUTH_URL=http://localhost:3000

# Email
RESEND_API_KEY=re_DYF23FWN_AnQNAuCodJR24qP19bP238R7
# OpenAI API Key for AI Agents
OPENAI_API_KEY=your-openai-api-key-here

#Session Management
MAX_SESSIONS_PER_USER=1

# Server Configuration
PORT=3000

# Social Provider Configuration (Optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret