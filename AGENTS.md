# AI Agent System - Development Guide

## Project Overview

**Hono Base** is a full-stack TypeScript application built with the Hono web framework that provides a robust AI Agent system for creating, managing, and executing AI-powered conversational agents. The system supports multiple agent types including chat, code generation, and research agents, all built on top of OpenAI's language models with additional AI provider support.

### Core Features

- **Multi-Agent Architecture**: Support for different agent types (chat, code, research, custom)
- **Real-time Communication**: Both synchronous and streaming chat capabilities
- **User Management**: Complete authentication system with email/password and OAuth providers (GitHub, Google)
- **Session Management**: Persistent chat sessions with execution tracking and configurable limits
- **Tool Integration**: Agent-specific tools for enhanced capabilities (code execution, web search, fact-checking)
- **API Documentation**: Auto-generated OpenAPI documentation via Scalar
- **Database Integration**: PostgreSQL with Prisma ORM for data persistence
- **Containerized Development**: Docker Compose setup for local development
- **JWT & Session Auth**: Dual authentication system supporting both JWT tokens and database sessions

### Target Users

- Developers building AI-powered applications
- Teams requiring conversational AI interfaces with specialized capabilities
- Applications needing multi-modal AI agents (chat, code generation, research)
- Systems requiring robust user authentication and session management
- Organizations needing scalable AI agent infrastructure

## Technology Stack & Tools

### Runtime Environment
- **Node.js Runtime**: Bun (high-performance JavaScript runtime)
- **TypeScript**: Full TypeScript support with strict configuration (ES2022, ESNext)
- **Framework**: Hono (lightweight, fast web framework)

### Core Dependencies
- **AI/ML**: `ai` SDK (`ai@5.0.22`) with OpenAI provider (`@ai-sdk/openai@2.0.19`)
- **Database**: PostgreSQL with Prisma ORM (`@prisma/client@6.15.0`, `prisma@6.15.0`)
- **Authentication**: Better Auth (`better-auth@1.3.7`) with Prisma adapter
- **Validation**: Zod for schema validation (`zod@4.1.1`, `@hono/zod-validator@0.7.2`)
- **Email**: Resend for transactional emails (`resend@6.0.1`, `@react-email/components@0.5.1`)
- **Documentation**: Scalar API reference (`@scalar/hono-api-reference@0.9.15`)
- **JWT**: JOSE library for JWT handling and verification

### Development Tools
- **Package Manager**: Bun with npm fallback support
- **Database Client**: PostgreSQL (`pg@8.16.3`, `postgres@3.4.7`)
- **Security**: bcryptjs for password hashing (`bcryptjs@3.0.2`)
- **Utilities**: UUID generation (`uuid@11.1.0`), React Email components
- **Database Tools**: Prisma Studio for database management

### Extended AI Support
- **OpenRouter API**: Support for alternative AI models via OpenRouter
- **Anthropic**: Support for Claude models (configured in environment)
- **Multi-Model Architecture**: Configurable AI providers per agent

## Development Workflow

### Essential Commands

```bash
# Development server with hot reload
bun run dev

# Build for production
bun run build

# Start production server
bun run start

# Database operations
bun run db:generate     # Generate Prisma client
bun run db:migrate      # Run database migrations
bun run db:migrate:reset # Reset database
bun run db:push         # Push schema changes
bun run db:studio       # Open Prisma Studio
bun run db:seed         # Seed database

# Package management
bun install             # Install dependencies
```

### Environment Setup

1. **Start database and services**:
   ```bash
   docker compose up -d --build
   ```

2. **Copy environment file**:
   ```bash
   cp .env.example .env
   ```

3. **Configure required variables**:
   ```env
   # Database Configuration
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=postgres  
   POSTGRES_DB=postgres
   DATABASE_URL=postgres://postgres:postgres@localhost:5432/postgres

   # Better Auth Configuration
   BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production
   BETTER_AUTH_URL=http://localhost:3000

   # AI API Keys
   OPENAI_API_KEY=your-openai-api-key-here
   ANTHROPIC_API_KEY=your-anthropic-api-key
   OPENROUTER_API_KEY=your-openrouter-api-key
   OPENROUTER_MODEL=openai/gpt-oss-20b:free
   OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

   # Email Configuration
   RESEND_API_KEY=your-resend-api-key

   # Session Management
   MAX_SESSIONS_PER_USER=1

   # Server Configuration
   PORT=3000

   # Social Provider Configuration (Optional)
   GITHUB_CLIENT_ID=your-github-client-id
   GITHUB_CLIENT_SECRET=your-github-client-secret
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   ```

4. **Install dependencies and setup**:
   ```bash
   bun install
   bun run db:migrate
   bun run dev
   ```

### Build and Deployment

- **Development**: [`bun run dev`](package.json:4) starts server with hot reload on port 3000
- **Production Build**: [`bun build src/index.ts --outdir ./dist`](package.json:5)
- **Production Start**: [`bun run dist/index.js`](package.json:6)
- **Docker**: Use [`docker-compose.yml`](docker-compose.yml:1) for PostgreSQL database with health checks

## Project Architecture

### Folder Structure

```
src/
├── agents/          # AI Agent implementations
│   ├── base/        # Base agent class and interfaces
│   │   └── agent.ts # Abstract BaseAgent class
│   ├── chat/        # Chat agent with Vietnamese language support
│   │   └── chat-agent.ts
│   ├── code/        # Code generation agent with tools
│   │   └── code-agent.ts
│   ├── research/    # Research agent with web search capabilities
│   │   └── research-agent.ts
│   └── index.ts     # Agent exports
├── config/          # Configuration files
│   └── session.ts   # Session configuration
├── db/              # Database connection and setup
│   └── index.ts     # Prisma client setup
├── lib/             # Shared utilities and services
│   ├── agents/      # Agent registry and executor
│   │   ├── executor.ts   # Agent execution and tracking
│   │   ├── registry.ts   # Agent lifecycle management
│   │   └── index.ts      # Agent system exports
│   ├── email/       # Email templates and services
│   │   ├── resend.ts     # Resend client configuration
│   │   ├── reset-password.tsx  # Password reset email template
│   │   └── invitation.tsx      # User invitation email template
│   ├── ai.ts        # AI/OpenAI utilities and wrappers
│   ├── auth.ts      # Better Auth configuration
│   └── validation.ts # Common validation schemas
├── middleware/      # HTTP middleware
│   ├── auth.ts      # Authentication middleware (JWT + Session)
│   ├── cors.ts      # CORS configuration
│   ├── scalar-docs.ts    # API documentation setup
│   └── session-limit.ts  # Session limiting middleware
├── modules/         # Feature modules (placeholder)
│   ├── auth/        # Authentication module
│   └── database/    # Database module
├── routes/          # API route handlers
│   ├── agents.ts    # Agent management and chat endpoints
│   ├── auth.ts      # Authentication routes
│   ├── users.ts     # User management routes
│   ├── posts.ts     # Post management routes
│   └── app.ts       # Application routes
├── types/           # TypeScript type definitions
│   └── index.ts     # Consolidated type exports
├── env.ts           # Environment variable management
└── index.ts         # Main application entry point
```

### Module Architecture

**Agent System Architecture**:
- [`BaseAgent`](src/agents/base/agent.ts:5) - Abstract base class for all agents with tool management
- [`AgentRegistry`](src/lib/agents/registry.ts:24) - Singleton registry managing agent lifecycles and health checks
- [`AgentExecutor`](src/lib/agents/executor.ts:23) - Handles agent execution, tracking, and cleanup
- Specialized agents with unique capabilities:
  - [`ChatAgent`](src/agents/chat/chat-agent.ts:4) - Vietnamese conversational AI
  - [`CodeAgent`](src/agents/code/code-agent.ts:4) - Programming and development assistant with tools
  - [`ResearchAgent`](src/agents/research/research-agent.ts:4) - Research and information gathering

**API Layer**:
- [`src/index.ts`](src/index.ts:1) - Main Hono application with middleware stack
- [`src/routes/`](src/routes/) - Modular route handlers with authentication and validation
- Middleware stack: CORS, logging, authentication, session limits, API documentation

**Data Layer**:
- [`prisma/schema.prisma`](prisma/schema.prisma:1) - Database schema with User, Session, Account, Post, Verification, and Jwks models
- [`src/db/index.ts`](src/db/index.ts) - Database connection setup
- Better Auth integration with Prisma adapter for session management

**Authentication Architecture**:
- Dual authentication system supporting both JWT and database sessions
- Better Auth configuration with email/password and OAuth providers
- Session limiting and cleanup mechanisms
- JWT verification with remote JWKS

### File Naming Conventions

- **Routes**: Kebab-case (e.g., `agents.ts`, `auth.ts`)
- **Classes/Components**: PascalCase (e.g., `BaseAgent`, `ChatAgent`)
- **Utilities**: Kebab-case (e.g., `session-limit.ts`, `scalar-docs.ts`)
- **Types**: Interfaces in PascalCase, files in kebab-case
- **Middleware**: Descriptive kebab-case (e.g., `auth.ts`, `cors.ts`)

### Import/Export Patterns

- **Barrel exports**: [`src/types/index.ts`](src/types/index.ts:1) consolidates type definitions
- **Default exports**: Route handlers and main components
- **Named exports**: Utilities, middleware, and shared functions
- **Relative imports**: Use relative paths within the same module
- **Absolute imports**: From `src/` for cross-module imports

## Development Guidelines

### Coding Standards

**TypeScript Configuration**:
- Strict mode enabled with [`tsconfig.json`](tsconfig.json:14)
- Target ES2022, module ESNext
- JSX support with Hono JSX import source
- Bun types included
- Isolated modules for better performance

**Agent Development Pattern**:
```typescript
// Extend BaseAgent for new agent types
class CustomAgent extends BaseAgent {
  constructor(config?: Partial<AgentConfig>) {
    const defaultConfig: AgentConfig = {
      id: 'custom-agent',
      name: 'Custom Agent',
      description: 'Custom agent description',
      model: 'gpt-3.5-turbo',
      systemPrompt: 'Your agent behavior...',
      temperature: 0.7,
      maxTokens: 2000,
      tools: ['tool1', 'tool2'],
      enabled: true,
      ...config
    }
    super(defaultConfig)
  }

  protected initializeTools(): void {
    // Register agent-specific tools
    const customTool: Tool = {
      name: 'custom-tool',
      description: 'Tool description',
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string' }
        },
        required: ['input']
      },
      execute: async (params, context) => {
        // Tool implementation
        return { result: 'Tool executed' }
      }
    }
    this.addTool(customTool)
  }
}
```

**API Route Pattern**:
```typescript
// Use Hono with proper typing
const routes = new Hono<{ Variables: Variables }>()

// Apply middleware and validation
routes.post('/endpoint',
  requireAuth,
  zValidator('json', schema),
  async (c) => {
    const user = c.get('user')
    const data = c.req.valid('json')
    
    try {
      // Handle request
      return c.json({ success: true, data })
    } catch (error) {
      console.error('Endpoint error:', error)
      return c.json({ 
        error: 'Operation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)
```

### Framework-Specific Conventions

**Hono Framework**:
- Use [`c.json()`](src/routes/agents.ts:44) for JSON responses
- Apply [`authMiddleware`](src/routes/agents.ts:17) for protected routes
- Use [`zValidator`](src/routes/agents.ts:76) with Zod schemas for request validation
- Leverage [`c.get('user')`](src/routes/agents.ts:78) for authenticated user data
- Consistent error handling with structured error responses

**Prisma ORM**:
- Use [`db`](src/lib/auth.ts:4) client from `src/db/index.ts`
- Follow [`snake_case`](prisma/schema.prisma:22) for database field mapping
- Use [`@map`](prisma/schema.prisma:22) annotations for field name mapping
- Include relations in queries when needed

**Better Auth Integration**:
- Configure in [`src/lib/auth.ts`](src/lib/auth.ts:8)
- Use Prisma adapter for database integration
- Support email/password and OAuth providers (GitHub, Google)
- Session management with database storage and configurable expiration

### Error Handling and Validation

**Validation with Zod**:
```typescript
const chatRequestSchema = z.object({
  message: z.string().min(1),
  sessionId: z.string().optional(),
  agentId: z.string().default('chat-agent')
})

const createAgentSchema = z.object({
  name: z.string().min(1),
  description: z.string(),
  type: z.enum(['chat', 'code', 'research', 'custom']),
  model: z.string().default('gpt-3.5-turbo'),
  systemPrompt: z.string().min(1),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().min(100).max(4000).optional()
})

// Use with Hono validator
zValidator('json', schema)
```

**Error Handling Pattern**:
```typescript
try {
  // Operation
  const result = await someOperation()
  return c.json({ success: true, data: result })
} catch (error) {
  console.error('Operation error:', error)
  return c.json({ 
    error: 'Operation failed',
    details: error instanceof Error ? error.message : 'Unknown error'
  }, 500)
}
```

### Testing Strategies

**Environment Variables**:
- Use [`src/env.ts`](src/env.ts:3) for centralized environment variable management
- Validate required variables: [`DATABASE_URL`](src/env.ts:5), [`OPENAI_API_KEY`](src/env.ts:10), [`BETTER_AUTH_SECRET`](src/env.ts:6)
- Support for multiple AI providers through environment configuration

**API Testing**:
- Use `/api/docs` endpoint for interactive API documentation
- Test with different agent types: chat, code, research
- Validate authentication flows and session management
- Test streaming and non-streaming endpoints

## Domain-Specific Knowledge

### AI Agent System

**Agent Types and Capabilities**:

1. **Chat Agent** ([`src/agents/chat/chat-agent.ts`](src/agents/chat/chat-agent.ts:4)):
   - General conversational AI with Vietnamese language support
   - Model: `gpt-3.5-turbo`, Temperature: `0.7`
   - Features: Time-aware responses, emoji support, friendly conversation
   - System prompt in Vietnamese with conversation guidelines

2. **Code Agent** ([`src/agents/code/code-agent.ts`](src/agents/code/code-agent.ts:4)):
   - Specialized for programming and software development
   - Model: `gpt-4`, Temperature: `0.3` (more deterministic)
   - Built-in tools:
     - `code-executor`: Execute code snippets safely
     - `syntax-checker`: Validate code syntax
     - `code-formatter`: Format code according to best practices
   - Multi-language support: JavaScript, TypeScript, Python, Java, C++, Rust, Go
   - Framework expertise: React, Vue, Angular, Node.js, Express, Hono

3. **Research Agent** ([`src/agents/research/research-agent.ts`](src/agents/research/research-agent.ts:4)):
   - Focused on information gathering and analysis
   - Model: `gpt-4`, Temperature: `0.4`
   - Built-in tools:
     - `web-search`: Search the web for current information
     - `fact-checker`: Verify facts and claims
     - `summarizer`: Summarize long texts or multiple sources
   - Research methodology with source citation and balanced perspectives

**Agent Lifecycle**:
1. **Registration**: Agents are registered in [`AgentRegistry`](src/lib/agents/registry.ts:24)
2. **Configuration**: Each agent has [`AgentConfig`](src/types/index.ts:10) with model, prompts, tools
3. **Initialization**: Tools are initialized via `initializeTools()` method
4. **Execution**: [`AgentExecutor`](src/lib/agents/executor.ts:23) manages execution and tracking
5. **Streaming**: Support for real-time streaming responses via `executeAgentStream()`

**Execution Flow**:
```typescript
// Basic execution
const context: AgentContext = {
  sessionId: 'session-id',
  userId: user.id,
  messages: [],
  variables: {}
}
const response = await executor.executeAgent(agentId, input, context, user.id)

// Streaming execution  
const stream = await executor.executeAgentStream(agentId, input, context, user.id)
return stream.toTextStreamResponse()
```

**Tool Architecture**:
```typescript
interface Tool {
  name: string
  description: string
  parameters: Record<string, any> // JSON Schema
  execute: (params: any, context: AgentContext) => Promise<any>
}
```

### Authentication System

**Better Auth Configuration**:
- Email/password authentication with password reset via Resend
- OAuth providers: GitHub and Google (configured via environment variables)
- Session management with database persistence (7-day expiration, 24-hour update age)
- Email verification system with HTML templates
- Disabled JWT plugin to prevent infinite loops

**Dual Authentication Support**:
- **JWT Authentication**: JOSE library for token verification with remote JWKS
- **Session Authentication**: Database-stored sessions with token lookup
- **Fallback Mechanism**: JWT verification falls back to session authentication
- **Authorization Header**: Supports `Bearer` token format

**Session Management**:
- Configurable session limits per user ([`MAX_SESSIONS_PER_USER`](src/env.ts:17))
- Session tracking with IP address and user agent
- Automatic session cleanup and expiration
- Session limit middleware with database cleanup

### Performance Considerations

**Agent Execution**:
- Execution tracking to prevent memory leaks
- Configurable token limits (`maxTokens`) and temperature settings per agent
- Execution history cleanup ([`cleanupExecutions`](src/lib/agents/executor.ts:162)) with configurable retention
- In-memory execution tracking with Map-based storage

**Database Optimization**:
- Proper indexing on user and session relationships
- Connection pooling with PostgreSQL
- Migration-based schema management
- Database health checks in Docker Compose setup

**Memory Management**:
- Singleton pattern for AgentRegistry to prevent multiple instances
- Map-based storage for executions with cleanup mechanisms
- Session limiting to prevent resource exhaustion

### Integration Patterns

**AI Provider Integration**:
- **OpenAI**: Primary AI provider via `@ai-sdk/openai`
- **OpenRouter**: Alternative AI models via OpenRouter API
- **Anthropic**: Claude models support (configured but not actively used)
- Centralized AI utilities in [`src/lib/ai.ts`](src/lib/ai.ts:1)
- Support for both [`generateText`](src/lib/ai.ts:10) and [`streamText`](src/lib/ai.ts:22) operations
- Model selection per agent configuration

**Email System Integration**:
- **Resend**: Transactional email service integration
- **React Email**: Component-based email templates
- Password reset and email verification flows
- Configured sender address via environment variables

**Database Integration**:
- **Prisma**: Type-safe database client with migrations
- **PostgreSQL**: Primary database with Docker Compose setup
- **Better Auth Adapter**: Seamless integration with authentication system

## API Reference

### Core Endpoints

**Agent Management**:
- `GET /api/agents` - List all registered agents with configurations
- `GET /api/agents/:id` - Get specific agent configuration by ID
- `POST /api/agents` - Create new agent (authenticated, requires agent schema validation)
- `PUT /api/agents/:id` - Update existing agent (authenticated, partial schema validation)
- `DELETE /api/agents/:id` - Delete agent from registry (authenticated)

**Agent Execution**:
- `POST /api/agents/chat` - Synchronous chat with agent (authenticated)
- `POST /api/agents/chat/stream` - Streaming chat with agent (authenticated)
- `GET /api/agents/executions` - Get execution history (authenticated, filterable by agentId)
- `GET /api/agents/stats` - Get execution statistics (authenticated, supports agentId filter)
- `GET /api/agents/:id/health` - Agent health check (public endpoint)

**Authentication** (Better Auth managed):
- Authentication endpoints provided by Better Auth at `/api/auth/*`
- Session management and user registration
- OAuth provider integration (GitHub, Google)
- Password reset and email verification

### Request/Response Formats

**Chat Request** ([`chatRequestSchema`](src/routes/agents.ts:23)):
```typescript
{
  message: string          // Required: User message
  sessionId?: string       // Optional: Session ID for continuity
  agentId: string         // Default: 'chat-agent'
}
```

**Chat Response**:
```typescript
{
  message: string          // Agent response
  sessionId: string        // Session ID (generated if not provided)
  agentId: string         // Agent ID that processed the request
  timestamp: Date         // Response timestamp
  usage?: {               // Token usage information (if available)
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}
```

**Create Agent Request** ([`createAgentSchema`](src/routes/agents.ts:29)):
```typescript
{
  name: string                    // Required: Agent name
  description: string             // Required: Agent description
  type: 'chat' | 'code' | 'research' | 'custom'  // Required: Agent type
  model?: string                  // Default: 'gpt-3.5-turbo'
  systemPrompt: string            // Required: System prompt
  temperature?: number            // Range: 0-1, optional
  maxTokens?: number             // Range: 100-4000, optional
  tools?: string[]               // Optional: Tool names
}
```

**Agent Configuration Response**:
```typescript
{
  id: string              // Unique agent identifier
  name: string           // Agent display name
  description: string    // Agent description
  model: string         // AI model being used
  systemPrompt: string  // System prompt/instructions
  temperature: number   // Model temperature (0-1)
  maxTokens: number    // Maximum tokens per response
  tools: string[]      // Available tools
  enabled: boolean     // Agent enabled status
}
```

**Execution Statistics Response**:
```typescript
{
  stats: {
    total: number        // Total executions
    completed: number    // Successful executions
    errors: number      // Failed executions
    running: number     // Currently running executions
    successRate: number // Success rate percentage
  }
}
```

### Error Response Format

```typescript
{
  error: string          // Error message
  details?: string       // Additional error details (if available)
}
```

## Maintenance Requirements

### Regular Updates

**Dependencies**:
- Keep AI SDK (`ai`, `@ai-sdk/openai`) updated for latest AI features
- Update Hono framework for performance and security improvements
- Maintain Prisma and database drivers for compatibility
- Update Better Auth for security patches

**Database Maintenance**:
- Monitor and clean up old executions via [`cleanupExecutions`](src/lib/agents/executor.ts:162)
- Review session management and cleanup policies
- Update schema migrations as needed
- Monitor database performance and connection pooling

**Agent System**:
- Update default agent configurations and system prompts
- Review and optimize agent tools and capabilities
- Monitor agent performance and usage statistics via `/api/agents/stats`
- Update AI model versions and provider integrations

**Environment Configuration**:
- Rotate authentication secrets regularly ([`BETTER_AUTH_SECRET`](src/env.ts:6))
- Update API keys for external services (OpenAI, Resend, OAuth providers)
- Review and update CORS settings for production deployment
- Monitor session limits and adjust [`MAX_SESSIONS_PER_USER`](src/env.ts:17) as needed

### Monitoring and Health Checks

**Agent Health Monitoring**:
- Use [`/api/agents/:id/health`](src/routes/agents.ts:249) for individual agent status
- Monitor agent registry health via [`checkAgentHealth`](src/lib/agents/registry.ts:114)
- Track agent execution success rates

**Execution Metrics**:
- Monitor via [`/api/agents/stats`](src/routes/agents.ts:236) endpoint
- Track execution history and cleanup old records
- Monitor memory usage of execution tracking

**Database Health**:
- Use Prisma Studio for database inspection and management
- Monitor PostgreSQL performance and connection health
- Utilize Docker Compose health checks for database availability

**API Documentation**:
- Accessible at `/api/docs` for interactive testing and validation
- Auto-generated OpenAPI specification via Scalar
- Regular testing of all endpoints with different agent types

### Security Considerations

**Authentication Security**:
- Regular rotation of JWT secrets and session tokens
- Monitor for unauthorized access attempts
- Implement rate limiting on authentication endpoints

**Agent Security**:
- Validate agent configurations to prevent malicious system prompts
- Implement tool execution sandboxing (especially for code execution)
- Monitor agent usage patterns for abuse

**Database Security**:
- Regular security updates for PostgreSQL
- Proper connection string management
- Database backup and recovery procedures

---

**Last Updated**: This guide should be updated whenever there are significant changes to:
- Project structure or architecture
- Agent system implementation
- Authentication flow or session management
- API endpoints or request/response formats
- Environment variables or configuration
- Database schema or migrations
- AI provider integrations or model updates

This ensures AI assistants have accurate and current information for effective contribution to the codebase.