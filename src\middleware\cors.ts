import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'

export const corsMiddleware = cors({
  origin: [
    'http://localhost:3000', 
    'http://localhost:5173',
    'http://localhost:8080',
    "*"
  ],
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
})

export const loggerMiddleware = logger()
export const prettyJSONMiddleware = prettyJSON()
