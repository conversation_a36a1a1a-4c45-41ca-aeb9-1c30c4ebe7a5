import { Context, Next } from 'hono'
import { db } from '../db'

// Middleware để giới hạn số lượng sessions active per user
export async function sessionLimitMiddleware(c: Context, next: Next) {
  try {
    // Lấy user ID từ context (sẽ được set bởi auth middleware)
    const user = c.get('user')
    if (!user) {
      await next()
      return
    }

    const userId = user.id
    const maxSessions = 2 // Giới hạn tối đa 2 sessions

    // Đếm số lượng sessions active của user
    const activeSessionsCount = await db.session.count({
      where: {
        userId: userId,
        expiresAt: {
          gt: new Date() // Chỉ đếm sessions chưa expire
        }
      }
    })

    console.log(`🔒 Session limit check - User ${userId}: ${activeSessionsCount}/${maxSessions} active sessions`)

    // Nếu vượt quá giới hạn, xóa sessions cũ nhất
    if (activeSessionsCount >= maxSessions) {
      console.log(`🔒 Session limit exceeded, cleaning up old sessions...`)

      // L<PERSON><PERSON> danh sách sessions cũ nhất (trừ session hiện tại nếu có)
      const currentSessionToken = c.req.header('Authorization')?.replace('Bearer ', '')
      const oldSessions = await db.session.findMany({
        where: {
          userId: userId,
          expiresAt: {
            gt: new Date()
          },
          ...(currentSessionToken && {
            token: {
              not: currentSessionToken // Không xóa session hiện tại
            }
          })
        },
        orderBy: {
          createdAt: 'asc' // Xóa sessions cũ nhất trước
        },
        take: activeSessionsCount - maxSessions + 1 // Xóa đủ để còn lại maxSessions - 1 (để dành chỗ cho session mới)
      })

      if (oldSessions.length > 0) {
        const deletedCount = await db.session.deleteMany({
          where: {
            id: {
              in: oldSessions.map(s => s.id)
            }
          }
        })

        console.log(`🔒 Cleaned up ${deletedCount} old sessions for user ${userId}`)
      }
    }

    await next()
  } catch (error) {
    console.error('Session limit middleware error:', error)
    await next()
  }
}

// Hàm utility để xóa tất cả sessions của user (để sử dụng trong logout)
export async function clearAllUserSessions(userId: string, exceptToken?: string) {
  try {
    const deleteResult = await db.session.deleteMany({
      where: {
        userId: userId,
        ...(exceptToken && {
          token: {
            not: exceptToken
          }
        })
      }
    })

    console.log(`🗑️ Cleared ${deleteResult.count} sessions for user ${userId}`)
    return deleteResult.count
  } catch (error) {
    console.error('Error clearing user sessions:', error)
    return 0
  }
}

// Hàm utility để lấy thông tin sessions của user
export async function getUserSessions(userId: string) {
  try {
    const sessions = await db.session.findMany({
      where: {
        userId: userId,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        token: true,
        createdAt: true,
        expiresAt: true,
        ipAddress: true,
        userAgent: true
      }
    })

    return sessions
  } catch (error) {
    console.error('Error getting user sessions:', error)
    return []
  }
}