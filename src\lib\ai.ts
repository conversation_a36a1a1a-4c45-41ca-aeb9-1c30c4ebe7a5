import { openai } from '@ai-sdk/openai'
import { generateText, streamText, convertToCoreMessages } from 'ai'

// Khởi tạo OpenAI client
const model = openai('gpt-3.5-turbo')

// Generate text
export async function generateAIText(prompt: string) {
    try {
        const { text } = await generateText({
            model,
            prompt,
        })
        return text
    } catch (error) {
        console.error('AI generation error:', error)
        throw new Error('Failed to generate AI response')
    }
}

// Stream text để có response realtime
export async function streamAIText(messages: any[]) {
    try {
        const result = await streamText({
            model,
            messages: convertToCoreMessages(messages),
        })
        return result.toTextStreamResponse()
    } catch (error) {
        console.error('AI streaming error:', error)
        throw new Error('Failed to stream AI response')
    }
}

// Chat completion
export async function chatCompletion(messages: any[]) {
    try {
        const { text } = await generateText({
            model,
            messages: convertToCoreMessages(messages),
        })
        return text
    } catch (error) {
        console.error('Chat completion error:', error)
        throw new Error('Failed to complete chat')
    }
}
