# ================================
# Langflow Environment Variables
# Based on official Langflow deployment documentation
# ================================

# AI Provider API Keys (BẮT BUỘC)
OPENAI_API_KEY=sk-your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
HUGGINGFACE_API_TOKEN=your-huggingface-token-here

# Langflow Configuration
LANGFLOW_AUTO_LOGIN=true
LANGFLOW_SAVE_DB_IN_CONFIG_DIR=true
LANGFLOW_LOAD_FLOWS_PATH=/app/flows
LANGFLOW_COMPONENTS_PATH=/app/components
LANGFLOW_STORE_ENVIRONMENT_VARIABLES=True
LANGFLOW_STORE=True

# Superuser Configuration
LANGFLOW_SUPERUSER=admin
LANGFLOW_SUPERUSER_PASSWORD=admin123

# https://fernetkeygen.com/
# openssl rand -hex 32
# Security
LANGFLOW_SECRET_KEY=your-secret-key-change-this-in-production

# Logging
LANGFLOW_LOG_LEVEL=INFO

# Development
LANGFLOW_DEV=false
LANGFLOW_REMOVE_API_KEYS=false

# ================================
# Hướng dẫn sử dụng:
# ================================
# 1. Copy file này: cp .env.example .env
# 2. Thay thế OPENAI_API_KEY bằng key thực tế của bạn
# 3. Chạy: docker-compose up -d
# 4. Truy cập: http://localhost:7860
# 5. Login: admin/admin123
# 
# Database: 
# - SQLite được tự động tạo và lưu trong container
# - LANGFLOW_SAVE_DB_IN_CONFIG_DIR=true cho persistent storage
# - Superuser được tự động tạo với credentials trên
# ================================