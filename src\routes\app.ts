import { Hono } from 'hono'


const appRoutes = new Hono()

appRoutes.get("/", (c) => {
    return c.json({
        message: "Welcome to Hono Full Stack API with AI Agents",
        version: "1.0.0",
        docs: "api/docs",
    });
});
// Health check
appRoutes.get("/health", (c) => {
    return c.json({
        status: "ok",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || "development",
        version: "1.0.0",
    });
});


export default appRoutes
