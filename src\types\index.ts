export interface User {
  id: number
  email: string
  name?: string
  image?: string
  createdAt: Date
  updatedAt: Date
}

export interface AgentConfig {
  id: string
  name: string
  description: string
  model: string
  systemPrompt: string
  temperature?: number
  maxTokens?: number
  tools?: string[]
  enabled: boolean
}

export interface AgentMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: Record<string, any>
}

export interface AgentContext {
  sessionId: string
  userId?: number
  messages: AgentMessage[]
  variables?: Record<string, any>
}

export interface AgentResponse {
  id: string
  content: string
  metadata?: Record<string, any>
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

export interface Tool {
  name: string
  description: string
  parameters: Record<string, any>
  execute: (params: any, context: AgentContext) => Promise<any>
}

export interface AgentExecution {
  id: string
  agentId: string
  userId?: number
  sessionId: string
  input: string
  output?: string
  status: 'pending' | 'running' | 'completed' | 'error'
  startTime: Date
  endTime?: Date
  error?: string
  metadata?: Record<string, any>
}

export type AgentType = 'chat' | 'code' | 'research' | 'custom'

export interface CreateAgentRequest {
  name: string
  description: string
  type: AgentType
  model?: string
  systemPrompt: string
  temperature?: number
  maxTokens?: number
  tools?: string[]
}

export interface UpdateAgentRequest extends Partial<CreateAgentRequest> {
  enabled?: boolean
}

export interface ChatRequest {
  message: string
  sessionId?: string
  agentId: string
}

export interface ChatResponse {
  message: string
  sessionId: string
  agentId: string
  timestamp: Date
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}
