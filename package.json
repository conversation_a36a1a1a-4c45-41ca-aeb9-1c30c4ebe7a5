{"name": "hono-base", "scripts": {"dev": "bun run --hot src/index.ts", "build": "bun build src/index.ts --outdir ./dist", "start": "bun run dist/index.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:reset": "prisma migrate reset", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@ai-sdk/openai": "^2.0.19", "@hono/zod-validator": "^0.7.2", "@prisma/client": "^6.15.0", "@react-email/components": "^0.5.1", "@scalar/hono-api-reference": "^0.9.15", "@types/bcryptjs": "^3.0.0", "@types/pg": "^8.15.5", "@types/uuid": "^10.0.0", "ai": "^5.0.22", "bcryptjs": "^3.0.2", "better-auth": "^1.3.7", "hono": "^4.9.4", "pg": "^8.16.3", "postgres": "^3.4.7", "resend": "^6.0.1", "uuid": "^11.1.0", "zod": "^4.1.1"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.3.0", "prisma": "^6.15.0"}}