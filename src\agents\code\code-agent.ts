import { BaseAgent } from '../base/agent'
import { AgentConfig, Tool } from '../../types'

export class CodeAgent extends BaseAgent {
  constructor(config?: Partial<AgentConfig>) {
    const defaultConfig: AgentConfig = {
      id: 'code-agent',
      name: 'Code Agent',
      description: 'Một AI agent chuyên về lập trình và phát triển phần mềm',
      model: 'gpt-4',
      systemPrompt: `<PERSON>ạ<PERSON> là một AI assistant chuyên về lập trình và phát triển phần mềm.
      Bạn có kinh nghiệm sâu về nhiều ngôn ngữ lập trình, frameworks, và best practices.
      
      Chuyên môn của bạn bao gồm:
      - JavaScript, TypeScript, Python, Java, C++, Rust, Go
      - Frontend: React, Vue, Angular, Svelte
      - Backend: Node.js, Express, Hono, FastAPI, Spring Boot
      - Database: PostgreSQL, MongoDB, Redis
      - DevOps: Docker, Kubernetes, CI/CD
      - Cloud: AWS, GCP, Azure, Vercel
      
      Khi được yêu cầu:
      - Cung cấp code examples rõ ràng và có comment
      - Giải thích logic và cách hoạt động
      - Đề xuất best practices và optimizations
      - Giúp debug và fix bugs
      - Review code và đưa ra feedback constructive`,
      temperature: 0.3,
      maxTokens: 4000,
      tools: ['code-executor', 'syntax-checker', 'code-formatter'],
      enabled: true,
      ...config
    }
    super(defaultConfig)
  }

  protected initializeTools(): void {
    // Code execution tool
    const codeExecutorTool: Tool = {
      name: 'code-executor',
      description: 'Execute code snippets safely',
      parameters: {
        type: 'object',
        properties: {
          language: { type: 'string', enum: ['javascript', 'python', 'typescript'] },
          code: { type: 'string' }
        },
        required: ['language', 'code']
      },
      execute: async (params) => {
        // Trong production, này sẽ execute code trong container sandbox
        return {
          output: `Code execution result for ${params.language}`,
          success: true
        }
      }
    }

    // Syntax checker tool
    const syntaxCheckerTool: Tool = {
      name: 'syntax-checker',
      description: 'Check syntax errors in code',
      parameters: {
        type: 'object',
        properties: {
          language: { type: 'string' },
          code: { type: 'string' }
        },
        required: ['language', 'code']
      },
      execute: async (params) => {
        // Implement syntax checking logic
        return {
          errors: [],
          warnings: [],
          valid: true
        }
      }
    }

    // Code formatter tool
    const codeFormatterTool: Tool = {
      name: 'code-formatter',
      description: 'Format code according to best practices',
      parameters: {
        type: 'object',
        properties: {
          language: { type: 'string' },
          code: { type: 'string' }
        },
        required: ['language', 'code']
      },
      execute: async (params) => {
        // Implement code formatting
        return {
          formattedCode: params.code, // Placeholder
          changes: []
        }
      }
    }

    this.addTool(codeExecutorTool)
    this.addTool(syntaxCheckerTool)
    this.addTool(codeFormatterTool)
  }

  protected buildSystemPrompt(): string {
    const basePrompt = super.buildSystemPrompt()
    
    return `${basePrompt}

Khi làm việc với code:

1. **Code Examples**: Luôn cung cấp examples hoàn chỉnh và chạy được
2. **Comments**: Thêm comments để giải thích logic phức tạp
3. **Error Handling**: Đề xuất proper error handling
4. **Performance**: Chú ý đến performance implications
5. **Security**: Highlight các security concerns nếu có
6. **Testing**: Đề xuất cách test code nếu phù hợp

Format response:
- Sử dụng markdown code blocks với syntax highlighting
- Giải thích step-by-step cho logic phức tạp
- Đưa ra alternatives nếu có nhiều cách giải quyết`
  }
}
