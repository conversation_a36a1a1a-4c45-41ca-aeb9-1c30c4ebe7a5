import { <PERSON>o } from "hono";
import { auth } from "../lib/auth";
import { getJWTToken } from "../middleware/auth";
import { createAuthClient } from "better-auth/client"
import { clearAllUserSessions } from "../middleware/session-limit";
import { getMaxSessionsPerUser } from "../config/session";

const authClient = createAuthClient({
  baseURL: "http://localhost:3000",
  plugins: [
    // organizationClient(),
    // oneTapClient({
    //     clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
    //     promptOptions: {
    //         maxAttempts: 1,
    //     },
    // }),
  ],
})
const authRoutes = new Hono();

// 1. Custom routes (ưu tiên cao hơn)
// JWT token endpoint - temporarily return session token
authRoutes.get("/token", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.header() as any,
    });

    if (!session?.session) {
      return c.json({ error: 'No active session' }, 401);
    }

    // Return session token instead of JWT to avoid infinite loop
    return c.json({
      token: session.session.token,
      user: session.user,
      session: session.session
    });
  } catch (error) {
    console.error('Get token error:', error);
    return c.json({ error: 'Failed to get token' }, 500);
  }
});

// Custom auth endpoints
authRoutes.get("/me", async (c) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.header() as any,
    });

    if (!session?.user) {
      return c.json({ error: "Not authenticated" }, 401);
    }

    return c.json({
      user: session.user,
      session: session.session,
    });
  } catch (error) {
    return c.json({ error: "Authentication failed" }, 500);
  }
});

authRoutes.post("/logout", async (c) => {
  try {
    // Get session token from Authorization header
    const authHeader = c.req.header('Authorization');
    const sessionToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!sessionToken) {
      return c.json({
        error: "No session token provided",
        success: false
      }, 400);
    }

    // Verify session exists and get user ID
    const { db } = await import('../db');
    const sessionData = await db.session.findFirst({
      where: {
        token: sessionToken,
        expiresAt: {
          gt: new Date()
        }
      },
      select: { userId: true }
    });

    if (!sessionData?.userId) {
      return c.json({
        error: "Invalid or expired session",
        success: false
      }, 401);
    }

    // Clear all user sessions from database
    const clearedCount = await clearAllUserSessions(sessionData.userId);

    if (clearedCount > 0) {
      return c.json({
        message: "Logged out successfully",
        success: true,
        sessionsCleared: clearedCount
      });
    } else {
      return c.json({
        error: "Failed to clear sessions",
        success: false
      }, 500);
    }
  } catch (error: any) {
    console.error('Logout error:', error);
    return c.json({
      error: "Logout failed",
      success: false,
      details: error?.message || 'Unexpected error'
    }, 500);
  }
});

// Sign-in endpoint
authRoutes.post("/sign-in", async (c) => {
  try {
    const { email, password } = await c.req.json();

    if (!email || !password) {
      return c.json({ error: "Email and password are required" }, 400);
    }

    const signInResult = await auth.api.signInEmail(
      {
        body: {
          email,
          password,
        }
      },

    )

    // 🔒 Áp dụng session limit sau khi sign-in thành công
    if (signInResult.token && signInResult.user) {
      const newSessionToken = signInResult.token
      const userId = signInResult.user.id

      if (userId && newSessionToken) {

        // Giới hạn sessions: chỉ giữ lại N sessions mới nhất
        const maxSessions = getMaxSessionsPerUser()

        const { db } = await import('../db')

        // Lấy tất cả sessions của user, sắp xếp theo thời gian tạo (mới nhất trước)
        const userSessions = await db.session.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          select: { id: true, token: true, createdAt: true }
        })


        // Nếu có quá nhiều sessions, xóa các sessions cũ (trừ session mới)
        if (userSessions.length > maxSessions) {
          const sessionsToDelete = userSessions.slice(maxSessions)
          console.log('🔒 SIGN-IN DEBUG - Deleting old sessions:', sessionsToDelete.length)

          await db.session.deleteMany({
            where: {
              id: { in: sessionsToDelete.map(s => s.id) }
            }
          })

        }
      }
    }

    return c.json(signInResult);
  } catch (error: any) {
    return c.json({ error: error.message || "Sign-in failed" }, 500);
  }
});

// Sign-up endpoint
authRoutes.post("/sign-up", async (c) => {
  try {
    const { email, password, name } = await c.req.json();

    if (!email || !password) {
      return c.json({ error: "Email and password are required" }, 400);
    }



    const signUpResult = await auth.api.signUpEmail({
      body: {
        email,
        password,
        name: name || email.split('@')[0], // Use part before @ as default name
      },
      headers: c.req.header() as any,
    });

    return c.json(signUpResult);
  } catch (error: any) {
    console.error("Sign-up error:", error);
    return c.json({ error: error.message || "Sign-up failed" }, 500);
  }
});

// JWKS endpoint for JWT verification - delegate to better-auth
authRoutes.get("/jwks", async (c) => {
  try {
    // Let better-auth handle JWKS directly
    return auth.handler(c.req.raw);
  } catch (error) {
    console.error('JWKS endpoint error:', error);
    return c.json({ error: "Failed to fetch JWKS" }, 500);
  }
});


// 3. Fallback cho Better Auth (các routes khác)
authRoutes.on(["POST", "GET"], "/*", (c) => auth.handler(c.req.raw));

// Endpoint để xem và quản lý sessions của user
// authRoutes.get("/sessions", async (c) => {
//   try {
//     // Lấy user từ session
//     const session = await auth.api.getSession({
//       headers: c.req.header() as any,
//     });

//     if (!session?.user) {
//       return c.json({ error: "Not authenticated" }, 401);
//     }

//     const { getUserSessions } = await import('../middleware/session-limit');
//     const userSessions = await getUserSessions(session.user.id);

//     return c.json({
//       sessions: userSessions,
//       count: userSessions.length,
//       maxAllowed: getMaxSessionsPerUser()
//     });
//   } catch (error) {
//     console.error('Get sessions error:', error);
//     return c.json({ error: 'Failed to get sessions' }, 500);
//   }
// });

// // Endpoint để xóa một session cụ thể
// authRoutes.delete("/sessions/:sessionId", async (c) => {
//   try {
//     const sessionId = c.req.param('sessionId');

//     // Lấy user từ session để xác thực quyền
//     const session = await auth.api.getSession({
//       headers: c.req.header() as any,
//     });

//     if (!session?.user) {
//       return c.json({ error: "Not authenticated" }, 401);
//     }

//     const { db } = await import('../db');

//     // Kiểm tra session có thuộc về user hiện tại không
//     const targetSession = await db.session.findFirst({
//       where: {
//         id: sessionId,
//         userId: session.user.id
//       }
//     });

//     if (!targetSession) {
//       return c.json({ error: "Session not found or not owned by user" }, 404);
//     }

//     // Xóa session
//     await db.session.delete({
//       where: { id: sessionId }
//     });

//     return c.json({
//       message: "Session deleted successfully",
//       deletedSessionId: sessionId
//     });
//   } catch (error) {
//     console.error('Delete session error:', error);
//     return c.json({ error: 'Failed to delete session' }, 500);
//   }
// });

export default authRoutes;