import { Hono } from "hono";
import {
  corsMiddleware,
  loggerMiddleware,
  prettyJSONMiddleware,
} from "./middleware/cors";
import { setupScalarDocs } from "./middleware/scalar-docs";

// Import routes
import agentsRoutes from "./routes/agents";
import authRoutes from "./routes/auth";
import postsRoutes from "./routes/posts";
import usersRoutes from "./routes/users";
import appRoutes from "./routes/app";

// Define types for Hono context
type Variables = {
  user: any;
  session: any;
};

const app = new Hono<{ Variables: Variables }>().basePath("/api");

// Global middleware
app.use("*", loggerMiddleware);
app.use("*", corsMiddleware);
app.use("*", prettyJSONMiddleware);


const appRouter = new Hono();
// Mount all API routes to the API app
appRouter.route("/app", appRoutes);
appRouter.route("/auth", authRoutes);
appRouter.route("/user", usersRoutes);
appRouter.route("/posts", postsRoutes);
appRouter.route("/agents", agentsRoutes);

// Mount the API app to main app

app.route("/", appRouter)

// Setup API documentation AFTER mounting routes
setupScalarDocs(app, {
  title: "Hono Full Stack API with AI Agents",
  theme: "purple",
  hiddenPaths: ["/", "/api/docs", "/api/docs/*"],
  path: "/docs",
  serverUrl: "/api"
});

// 404 handler
app.notFound((c) => {
  return c.json({ error: "Not found" }, 404);
});

// Error handler
app.onError((err, c) => {
  console.error(`${err}`);
  return c.json(
    {
      error: "Internal Server Error",
      message: err.message,
    },
    500
  );
});

console.log(`🚀 Server starting on port ${process.env.PORT || 3000}`);

export default {
  port: process.env.PORT || 3000,
  fetch: app.fetch,
};
