import { env } from '../env'

/**
 * Session configuration
 * Centralized configuration for session management
 */
export const sessionConfig = {
  /**
   * Maximum number of active sessions allowed per user
   * Default: 2 sessions per user
   */
  maxSessionsPerUser: parseInt(env.MAX_SESSIONS_PER_USER || '2'),

  /**
   * Session cleanup settings
   */
  cleanup: {
    /**
     * Whether to enable automatic session cleanup on sign-in
     */
    enabled: true,

    /**
     * Strategy for session cleanup: 'oldest' | 'newest'
     * - 'oldest': Remove oldest sessions first
     * - 'newest': Remove newest sessions first (keep oldest)
     */
    strategy: 'oldest' as 'oldest' | 'newest',
  },

  /**
   * Session validation settings
   */
  validation: {
    /**
     * Whether to validate session on every request
     */
    enabled: true,

    /**
     * Whether to apply session limit on every authenticated request
     */
    applyLimitOnRequest: false,
  }
} as const

/**
 * Get maximum sessions per user
 * @returns number of maximum sessions allowed
 */
export function getMaxSessionsPerUser(): number {
  return sessionConfig.maxSessionsPerUser
}

/**
 * Check if session cleanup is enabled
 * @returns boolean indicating if cleanup is enabled
 */
export function isSessionCleanupEnabled(): boolean {
  return sessionConfig.cleanup.enabled
}

/**
 * Get session cleanup strategy
 * @returns cleanup strategy
 */
export function getSessionCleanupStrategy(): 'oldest' | 'newest' {
  return sessionConfig.cleanup.strategy
}