/**
 * Agent Executor - <PERSON><PERSON><PERSON><PERSON> thi và quản lý vòng đời của agent executions
 *
 * Chịu trách nhiệm:
 * - Thực thi agents với input và context
 * - Quản lý execution tracking và logging
 * - Xử lý streaming responses
 * - Cleanup executions cũ để tránh memory leak
 * - <PERSON> dõi thống kê và metrics
 *
 * <PERSON><PERSON>ch sử dụng:
 * ```typescript
 * const executor = new AgentExecutor()
 * const response = await executor.executeAgent('chat-agent', 'Hello', context)
 * const stream = await executor.executeAgentStream('chat-agent', 'Hello', context)
 * ```
 */

import { AgentRegistry } from './registry'
import { AgentContext, AgentResponse, AgentExecution } from '../../types'
import { v4 as uuidv4 } from 'uuid'

export class AgentExecutor {
  private registry: AgentRegistry
  private executions: Map<string, AgentExecution> = new Map()

  constructor() {
    this.registry = AgentRegistry.getInstance()
  }

  async executeAgent(
    agentId: string,
    input: string,
    context: AgentContext,
    userId?: number
  ): Promise<AgentResponse> {
    const agent = this.registry.getAgent(agentId)
    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`)
    }

    if (!agent.getConfig().enabled) {
      throw new Error(`Agent ${agentId} is disabled`)
    }

    // Create execution record
    const executionId = uuidv4()
    const execution: AgentExecution = {
      id: executionId,
      agentId,
      userId,
      sessionId: context.sessionId,
      input,
      status: 'pending',
      startTime: new Date()
    }

    this.executions.set(executionId, execution)

    try {
      // Update status to running
      execution.status = 'running'
      this.executions.set(executionId, execution)

      // Execute the agent
      const response = await agent.execute(input, context)

      // Update execution with success
      execution.status = 'completed'
      execution.output = response.content
      execution.endTime = new Date()
      execution.metadata = {
        usage: response.usage
      }
      this.executions.set(executionId, execution)

      return response
    } catch (error) {
      // Update execution with error
      execution.status = 'error'
      execution.error = error instanceof Error ? error.message : 'Unknown error'
      execution.endTime = new Date()
      this.executions.set(executionId, execution)

      throw error
    }
  }

  async executeAgentStream(
    agentId: string,
    input: string,
    context: AgentContext,
    userId?: number
  ) {
    const agent = this.registry.getAgent(agentId)
    if (!agent) {
      throw new Error(`Agent with ID ${agentId} not found`)
    }

    if (!agent.getConfig().enabled) {
      throw new Error(`Agent ${agentId} is disabled`)
    }

    // Create execution record
    const executionId = uuidv4()
    const execution: AgentExecution = {
      id: executionId,
      agentId,
      userId,
      sessionId: context.sessionId,
      input,
      status: 'running',
      startTime: new Date()
    }

    this.executions.set(executionId, execution)

    try {
      const stream = await agent.executeStream(input, context)
      return stream
    } catch (error) {
      execution.status = 'error'
      execution.error = error instanceof Error ? error.message : 'Unknown error'
      execution.endTime = new Date()
      this.executions.set(executionId, execution)
      throw error
    }
  }

  getExecution(executionId: string): AgentExecution | undefined {
    return this.executions.get(executionId)
  }

  getExecutionHistory(userId?: number, agentId?: string): AgentExecution[] {
    return Array.from(this.executions.values()).filter(execution => {
      if (userId && execution.userId !== userId) return false
      if (agentId && execution.agentId !== agentId) return false
      return true
    })
  }

  getExecutionStats(agentId?: string) {
    const executions = agentId 
      ? Array.from(this.executions.values()).filter(e => e.agentId === agentId)
      : Array.from(this.executions.values())

    const total = executions.length
    const completed = executions.filter(e => e.status === 'completed').length
    const errors = executions.filter(e => e.status === 'error').length
    const running = executions.filter(e => e.status === 'running').length

    return {
      total,
      completed,
      errors,
      running,
      successRate: total > 0 ? (completed / total) * 100 : 0
    }
  }

  // Cleanup old executions (để tránh memory leak)
  cleanupExecutions(olderThanHours: number = 24) {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000)
    
    for (const [id, execution] of this.executions.entries()) {
      if (execution.startTime < cutoffTime) {
        this.executions.delete(id)
      }
    }
  }
}
