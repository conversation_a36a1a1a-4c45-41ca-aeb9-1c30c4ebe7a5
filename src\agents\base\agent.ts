import { <PERSON>Config, AgentContext, AgentResponse, Tool } from '../../types'
import { generateText, streamText } from 'ai'
import { openai } from '@ai-sdk/openai'

export abstract class BaseAgent {
  protected config: AgentConfig
  protected tools: Map<string, Tool> = new Map()

  constructor(config: AgentConfig) {
    this.config = config
    this.initializeTools()
  }

  protected abstract initializeTools(): void

  protected getModel() {
    return openai(this.config.model || 'gpt-3.5-turbo')
  }

  protected buildSystemPrompt(): string {
    return this.config.systemPrompt
  }

  protected buildMessages(context: AgentContext) {
    const systemMessage = {
      role: 'system' as const,
      content: this.buildSystemPrompt()
    }

    return [
      systemMessage,
      ...context.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    ]
  }

  async execute(input: string, context: AgentContext): Promise<AgentResponse> {
    try {
      const messages = this.buildMessages({
        ...context,
        messages: [...context.messages, {
          id: Date.now().toString(),
          role: 'user',
          content: input,
          timestamp: new Date()
        }]
      })

      const model = this.getModel()
      const result = await generateText({
        model,
        messages,
        temperature: this.config.temperature || 0.7,
        maxTokens: this.config.maxTokens || 2000,
      })

      return {
        id: Date.now().toString(),
        content: result.text,
        usage: result.usage ? {
          promptTokens: result.usage.promptTokens || 0,
          completionTokens: result.usage.completionTokens || 0,
          totalTokens: result.usage.totalTokens || 0
        } : undefined
      }
    } catch (error) {
      throw new Error(`Agent execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async executeStream(input: string, context: AgentContext) {
    try {
      const messages = this.buildMessages({
        ...context,
        messages: [...context.messages, {
          id: Date.now().toString(),
          role: 'user',
          content: input,
          timestamp: new Date()
        }]
      })

      const model = this.getModel()
      const result = await streamText({
        model,
        messages,
        temperature: this.config.temperature || 0.7,
      })

      return result
    } catch (error) {
      throw new Error(`Agent stream execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  addTool(tool: Tool) {
    this.tools.set(tool.name, tool)
  }

  removeTool(name: string) {
    this.tools.delete(name)
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name)
  }

  getConfig(): AgentConfig {
    return { ...this.config }
  }

  updateConfig(updates: Partial<AgentConfig>) {
    this.config = { ...this.config, ...updates }
  }
}
