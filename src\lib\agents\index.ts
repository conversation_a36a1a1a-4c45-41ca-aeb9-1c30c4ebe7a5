/**
 * Agent Management & Execution Layer
 *
 * This module provides the core infrastructure for managing and executing AI agents.
 * It includes the AgentRegistry for managing agent instances and AgentExecutor for
 * handling agent execution, streaming, and execution tracking.
 */

// Core management classes
export { AgentRegistry } from './registry'
export { AgentExecutor } from './executor'

// Re-export agent implementations for convenience
// This allows importing agents from a single location: import { ChatAgent } from '../lib/agents'
export { BaseAgent } from '../../agents/base/agent'
export { ChatAgent } from '../../agents/chat/chat-agent'
export { CodeAgent } from '../../agents/code/code-agent'
export { ResearchAgent } from '../../agents/research/research-agent'
