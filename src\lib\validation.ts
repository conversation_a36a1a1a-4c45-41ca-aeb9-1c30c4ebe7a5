import { z } from 'zod'

// User schemas
export const createUserSchema = z.object({
    email: z.string().email(),
    name: z.string().min(1).max(255),
    password: z.string().min(6),
})

export const updateUserSchema = z.object({
    name: z.string().min(1).max(255).optional(),
    email: z.string().email().optional(),
})

export const loginSchema = z.object({
    email: z.string().email(),
    password: z.string().min(6),
})

// Post schemas
export const createPostSchema = z.object({
    title: z.string().min(1).max(255),
    content: z.string().optional(),
    published: z.boolean().default(false),
})

export const updatePostSchema = z.object({
    title: z.string().min(1).max(255).optional(),
    content: z.string().optional(),
    published: z.boolean().optional(),
})

// AI schemas
export const aiPromptSchema = z.object({
    prompt: z.string().min(1),
})

export const chatMessageSchema = z.object({
    messages: z.array(z.object({
        role: z.enum(['user', 'assistant', 'system']),
        content: z.string(),
    })),
})

// Common schemas
export const paginationSchema = z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
})
