import { Hono } from 'hono'
import { z<PERSON>alidator } from '@hono/zod-validator'
import { z } from 'zod'
import { AgentRegistry, AgentExecutor } from '../lib/agents'
import { authMiddleware, requireAuth } from '../middleware/auth'
import { v4 as uuidv4 } from 'uuid'

// Define types for Hono context
type Variables = {
  user: any
  session: any
}

const agentsRoutes = new Hono<{ Variables: Variables }>()

// Apply auth middleware
agentsRoutes.use('*', authMiddleware)

const registry = AgentRegistry.getInstance()
const executor = new AgentExecutor()

// Validation schemas
const chatRequestSchema = z.object({
  message: z.string().min(1),
  sessionId: z.string().optional(),
  agentId: z.string().default('chat-agent')
})

const createAgentSchema = z.object({
  name: z.string().min(1),
  description: z.string(),
  type: z.enum(['chat', 'code', 'research', 'custom']),
  model: z.string().default('gpt-3.5-turbo'),
  systemPrompt: z.string().min(1),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().min(100).max(4000).optional(),
  tools: z.array(z.string()).optional()
})

// Get all agents
agentsRoutes.get('/', async (c) => {
  try {
    const agents = registry.listAgentConfigs()
    return c.json({
      agents,
      total: agents.length
    })
  } catch (error) {
    console.error('Get agents error:', error)
    return c.json({ error: 'Failed to fetch agents' }, 500)
  }
})

// Get agent by ID
agentsRoutes.get('/:id', async (c) => {
  const agentId = c.req.param('id')
  
  try {
    const agent = registry.getAgent(agentId)
    if (!agent) {
      return c.json({ error: 'Agent not found' }, 404)
    }

    return c.json({
      agent: agent.getConfig()
    })
  } catch (error) {
    console.error('Get agent error:', error)
    return c.json({ error: 'Failed to fetch agent' }, 500)
  }
})

// Create new agent
agentsRoutes.post('/', 
  requireAuth,
  zValidator('json', createAgentSchema),
  async (c) => {
    const user = c.get('user')
    const data = c.req.valid('json')

    try {
      const agentConfig = {
        id: `${data.type}-${uuidv4()}`,
        name: data.name,
        description: data.description,
        model: data.model,
        systemPrompt: data.systemPrompt,
        temperature: data.temperature || 0.7,
        maxTokens: data.maxTokens || 2000,
        tools: data.tools || [],
        enabled: true
      }

      const agent = registry.createAgent(agentConfig)

      return c.json({
        message: 'Agent created successfully',
        agent: agent.getConfig()
      }, 201)
    } catch (error) {
      console.error('Create agent error:', error)
      return c.json({ error: 'Failed to create agent' }, 500)
    }
  }
)

// Update agent
agentsRoutes.put('/:id',
  requireAuth,
  zValidator('json', createAgentSchema.partial()),
  async (c) => {
    const agentId = c.req.param('id')
    const data = c.req.valid('json')

    try {
      const success = registry.updateAgent(agentId, data)
      if (!success) {
        return c.json({ error: 'Agent not found' }, 404)
      }

      const updatedAgent = registry.getAgent(agentId)
      return c.json({
        message: 'Agent updated successfully',
        agent: updatedAgent?.getConfig()
      })
    } catch (error) {
      console.error('Update agent error:', error)
      return c.json({ error: 'Failed to update agent' }, 500)
    }
  }
)

// Delete agent
agentsRoutes.delete('/:id', requireAuth, async (c) => {
  const agentId = c.req.param('id')

  try {
    const success = registry.unregisterAgent(agentId)
    if (!success) {
      return c.json({ error: 'Agent not found' }, 404)
    }

    return c.json({ message: 'Agent deleted successfully' })
  } catch (error) {
    console.error('Delete agent error:', error)
    return c.json({ error: 'Failed to delete agent' }, 500)
  }
})

// Chat with agent
agentsRoutes.post('/chat',
  requireAuth,
  zValidator('json', chatRequestSchema),
  async (c) => {
    const user = c.get('user')
    const { message, sessionId, agentId } = c.req.valid('json')

    try {
      const actualSessionId = sessionId || uuidv4()
      
      const context = {
        sessionId: actualSessionId,
        userId: user.id,
        messages: [], // In production, load from database
        variables: {}
      }

      const response = await executor.executeAgent(agentId, message, context, user.id)

      return c.json({
        message: response.content,
        sessionId: actualSessionId,
        agentId,
        timestamp: new Date(),
        usage: response.usage
      })
    } catch (error) {
      console.error('Chat error:', error)
      return c.json({ 
        error: 'Failed to process chat request',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// Stream chat with agent
agentsRoutes.post('/chat/stream',
  requireAuth,
  zValidator('json', chatRequestSchema),
  async (c) => {
    const user = c.get('user')
    const { message, sessionId, agentId } = c.req.valid('json')

    try {
      const actualSessionId = sessionId || uuidv4()
      
      const context = {
        sessionId: actualSessionId,
        userId: user.id,
        messages: [], // In production, load from database
        variables: {}
      }

      const stream = await executor.executeAgentStream(agentId, message, context, user.id)
      
      return stream.toTextStreamResponse()
    } catch (error) {
      console.error('Stream chat error:', error)
      return c.json({ 
        error: 'Failed to process stream chat request',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// Get execution history
agentsRoutes.get('/executions', requireAuth, async (c) => {
  const user = c.get('user')
  const agentId = c.req.query('agentId')

  try {
    const executions = executor.getExecutionHistory(user.id, agentId)
    return c.json({
      executions,
      total: executions.length
    })
  } catch (error) {
    console.error('Get executions error:', error)
    return c.json({ error: 'Failed to fetch execution history' }, 500)
  }
})

// Get execution stats
agentsRoutes.get('/stats', requireAuth, async (c) => {
  const agentId = c.req.query('agentId')

  try {
    const stats = executor.getExecutionStats(agentId)
    return c.json({ stats })
  } catch (error) {
    console.error('Get stats error:', error)
    return c.json({ error: 'Failed to fetch stats' }, 500)
  }
})

// Health check for agent
agentsRoutes.get('/:id/health', async (c) => {
  const agentId = c.req.param('id')

  try {
    const healthy = registry.checkAgentHealth(agentId)
    return c.json({
      agentId,
      healthy,
      status: healthy ? 'active' : 'inactive'
    })
  } catch (error) {
    console.error('Health check error:', error)
    return c.json({ error: 'Failed to check agent health' }, 500)
  }
})

export default agentsRoutes
