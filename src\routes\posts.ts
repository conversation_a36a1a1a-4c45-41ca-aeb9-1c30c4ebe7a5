import { Hono } from 'hono'
import { db } from '../db'
import { authMiddleware, requireAuth } from '../middleware/auth'
import { zValidator } from '@hono/zod-validator'
import { createPostSchema, updatePostSchema, paginationSchema } from '../lib/validation'

// Define types for Hono context
type Variables = {
  user: any
  session: any
}

const postsRoutes = new Hono<{ Variables: Variables }>()

// Apply auth middleware to all routes
postsRoutes.use('*', authMiddleware)

// Get all posts (with pagination)
postsRoutes.get('/', zValidator('query', paginationSchema), async (c) => {
  const { page, limit } = c.req.valid('query')
  const offset = (page - 1) * limit

  try {
    const allPosts = await db.post.findMany({
      where: {
        published: true
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit
    })

    return c.json({
      posts: allPosts,
      pagination: {
        page,
        limit,
      }
    })
  } catch (error) {
    console.error('Get posts error:', error)
    return c.json({ error: 'Failed to fetch posts' }, 500)
  }
})

// Get post by ID
postsRoutes.get('/:id', async (c) => {
  const id = c.req.param('id')

  try {
    const post = await db.post.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    if (!post) {
      return c.json({ error: 'Post not found' }, 404)
    }

    return c.json({ post })
  } catch (error) {
    console.error('Get post error:', error)
    return c.json({ error: 'Failed to fetch post' }, 500)
  }
})

// Create new post
postsRoutes.post('/',
  requireAuth,
  zValidator('json', createPostSchema),
  async (c) => {
    const user = c.get('user')
    const data = c.req.valid('json')

    try {
      const postData: {
        title: string
        content?: string
        published: boolean
        authorId: string
      } = {
        title: data.title,
        published: data.published,
        authorId: String(user.id),
      }

      if (data.content !== undefined) {
        postData.content = data.content
      }

      const newPost = await db.post.create({
        data: postData as any
      })

      return c.json({
        message: 'Post created successfully',
        post: newPost
      }, 201)
    } catch (error) {
      console.error('Create post error:', error)
      return c.json({ error: 'Failed to create post' }, 500)
    }
  }
)

// Update post
postsRoutes.put('/:id',
  requireAuth,
  zValidator('json', updatePostSchema),
  async (c) => {
    const user = c.get('user')
    const data = c.req.valid('json')
    const id = c.req.param('id')

    try {
      // Check if user owns the post
      const existingPost = await db.post.findUnique({
        where: { id },
        select: { authorId: true }
      })

      if (!existingPost) {
        return c.json({ error: 'Post not found' }, 404)
      }

      if (existingPost.authorId !== user.id) {
        return c.json({ error: 'Unauthorized to update this post' }, 403)
      }

      const updatedPost = await db.post.update({
        where: { id },
        data: {
          ...data,
          updatedAt: new Date(),
        }
      })

      return c.json({
        message: 'Post updated successfully',
        post: updatedPost
      })
    } catch (error) {
      console.error('Update post error:', error)
      return c.json({ error: 'Failed to update post' }, 500)
    }
  }
)

// Delete post
postsRoutes.delete('/:id', requireAuth, async (c) => {
  const user = c.get('user')
  const id = c.req.param('id')

  try {
    // Check if user owns the post
    const existingPost = await db.post.findUnique({
      where: { id },
      select: { authorId: true }
    })

    if (!existingPost) {
      return c.json({ error: 'Post not found' }, 404)
    }

    if (existingPost.authorId !== user.id) {
      return c.json({ error: 'Unauthorized to delete this post' }, 403)
    }

    await db.post.delete({
      where: { id }
    })

    return c.json({ message: 'Post deleted successfully' })
  } catch (error) {
    console.error('Delete post error:', error)
    return c.json({ error: 'Failed to delete post' }, 500)
  }
})

// Get current user's posts
postsRoutes.get('/my-posts',
  requireAuth,
  zValidator('query', paginationSchema),
  async (c) => {
    const user = c.get('user')
    const { page, limit } = c.req.valid('query')
    const offset = (page - 1) * limit

    try {
      const userPosts = await db.post.findMany({
        where: {
          authorId: user.id
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      })

      return c.json({
        posts: userPosts,
        pagination: {
          page,
          limit,
        }
      })
    } catch (error) {
      console.error('Get user posts error:', error)
      return c.json({ error: 'Failed to fetch your posts' }, 500)
    }
  }
)

export default postsRoutes
